/*
 * @file 任意门Lottie动画管理
 * <AUTHOR>
 * @date 2025-03-15 10:00:00
 */
import lottie from 'lottie-web';

// 导入点击手势动画数据
import clickPointerData from '@/pages/liveshow/assets/click-pointer.json';
import aiHelperData from '@/pages/liveshow/assets/ai-helper.json';


// 动画类型映射
const ANIMATION_TYPES = {
    'click-pointer': clickPointerData,
    'ai-helper': aiHelperData
};

// 缓存已加载的动画实例
const cacheMap = {};

/**
 * Lottie动画管理类
 */
class LottieManager {
    /**
     * 初始化Lottie动画
     * @param {HTMLElement} container - 动画容器元素
     * @param {string} type - 动画类型
     * @param {Object} options - 动画选项
     * @returns {AnimationItem|null} 动画实例
     */
    static initLottie(
        container,
        type,
        options = {}
    ) {
        if (!container || !type || !ANIMATION_TYPES[type]) {
            return null;
        }

        // 清理之前的动画实例
        this.destroyLottie(type);

        // 默认选项
        const defaultOptions = {
            interval: 2000, // 默认2秒间隔
            loop: false, // 默认不循环
            autoplay: true, // 默认自动播放
            renderer: 'svg' // 默认SVG渲染器
        };

        // 合并选项
        const mergedOptions = {...defaultOptions, ...options};
        const {interval, loop, autoplay, renderer} = mergedOptions;

        // 创建新的动画实例
        const animation = lottie.loadAnimation({
            container,
            renderer,
            loop,
            autoplay,
            animationData: ANIMATION_TYPES[type]
        });

        // 如果设置了间隔且不循环，添加完成事件监听器
        if (interval && !loop) {
            animation.addEventListener('complete', () => {
                // 动画完成后，等待指定间隔再重新播放
                setTimeout(() => {
                    animation.goToAndPlay(0, true);
                }, interval);
            });
        }

        // 缓存动画实例
        cacheMap[type] = animation;

        return animation;
    }

    /**
     * 销毁Lottie动画
     * @param {string} type - 动画类型
     */
    static destroyLottie(type) {
        if (cacheMap[type]) {
            cacheMap[type].destroy();
            delete cacheMap[type];
        }
    }

    /**
     * 获取动画数据
     * @param {string} type - 动画类型
     * @returns {Object|null} 动画数据
     */
    static getLottieData(type) {
        return ANIMATION_TYPES[type] || null;
    }

    /**
     * 获取已缓存的动画实例
     * @param {string} type - 动画类型
     * @returns {AnimationItem|null} 动画实例
     */
    static getCachedAnimation(type) {
        return cacheMap[type] || null;
    }

    /**
     * 添加新的动画类型
     * @param {string} type - 动画类型
     * @param {Object} animationData - 动画数据
     */
    static addAnimationType(type, animationData) {
        if (type && animationData) {
            ANIMATION_TYPES[type] = animationData;
        }
    }

    /**
     * 暂停动画
     * @param {string} type - 动画类型
     */
    static pauseAnimation(type) {
        const animation = this.getCachedAnimation(type);
        if (animation) {
            animation.pause();
        }
    }

    /**
     * 播放动画
     * @param {string} type - 动画类型
     */
    static playAnimation(type) {
        const animation = this.getCachedAnimation(type);
        if (animation) {
            animation.play();
        }
    }

    /**
     * 停止动画
     * @param {string} type - 动画类型
     */
    static stopAnimation(type) {
        const animation = this.getCachedAnimation(type);
        if (animation) {
            animation.stop();
        }
    }
}

export default LottieManager;
