/*
 * @file AI助手提示可见性控制
 * <AUTHOR>
 * @date 2025-08-06 14:19:07
 */

import {useEffect, useRef, useState} from 'react';
import {useAdLiveShowContext} from '../context';

export function useAiHelperTips() {
    const {roomVideo = {}} = useAdLiveShowContext();
    const {roomVideoSwitch} = roomVideo;
    const [aiHelperTipsVisible, setAiHelperTipsVisible] = useState(false);
    const [shouldScroll, setShouldScroll] = useState(false);
    const contentRef = useRef(null);
    const cIt = (roomVideo.videos || []).find(i => i.matchVideoFlag);
    const currrentVideoTitle = cIt?.title || '';
    const currrentVideoID = cIt?.videoId || '';

    const checkTextOverflow = () => {
        const container = contentRef.current;
        if (container) {
            setShouldScroll(container.scrollWidth > container.clientWidth);
        }
    };

    useEffect(() => {
        if (roomVideoSwitch === 1) {
            setAiHelperTipsVisible(true);
            setTimeout(checkTextOverflow, 100); // 确保 DOM 渲染
        }
    }, [roomVideoSwitch]);

    useEffect(() => {
        if (aiHelperTipsVisible && currrentVideoTitle) {
            setTimeout(checkTextOverflow, 100);
        }
    }, [currrentVideoTitle, aiHelperTipsVisible]);

    return {
        aiHelperTipsVisible,
        shouldScroll,
        currrentVideoTitle,
        contentRef,
        setAiHelperTipsVisible,
        currrentVideoID
    };
}
