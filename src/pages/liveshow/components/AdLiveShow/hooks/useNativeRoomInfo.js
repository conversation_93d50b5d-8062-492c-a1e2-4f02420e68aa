/*
 * @file useNativeRoomInfo
 * <AUTHOR>
 * @date 2025-08-11 16:53:05
 */

import {useState, useEffect} from 'react';
import {aiHelperNativeUtils} from '@/components/AIHelper/utils';

export function useNativeRoomInfo() {
    const [clueExt, setClueExt] = useState({});

    useEffect(() => {
        let isMounted = true;
        (async () => {
            try {
                const res = await aiHelperNativeUtils.getRoomInfo();
                if (isMounted) {
                    setClueExt(res?.data?.clueExt || {});
                }
            }
            catch (err) {
                console.error('Failed to get room info:', err);
                if (isMounted) {
                    setClueExt({});
                }
            }
        })();
        return () => {
            isMounted = false;
        };
    }, []);

    return clueExt;
}
