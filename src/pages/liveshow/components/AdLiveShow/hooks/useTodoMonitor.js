import {useEffect} from 'react';
import {adCommonShowLog} from '../monitor/ad';

export function useTodoMonitor({
    nid, screen, status, userstat
}) {
    useEffect(() => {
        if (location.href.includes('ubcdatalogdev')) {
            const serverIds = ['19535', '19532'];
            serverIds.forEach(serverId => {
                adCommonShowLog({
                    page: status,
                    type: 'enter',
                    value: 'entry',
                    nid,
                    screen,
                    serverId,
                    isNewVersion: true,
                    userstat
                });
            });
        }
    }, [nid, screen, status, userstat]);
}