@import (reference) '../../../assets/style-util.less';

.ad-chat-msg-wrapper {
    width: 100%;
    bottom: 144pr;
    height: 26vh;
    max-height: 630pr;
    padding: 0 30pr 0;
    color: #fff;
    overflow: hidden;
    mask: linear-gradient(transparent, #fff 15%);

    .last-msg {
        position: absolute;
        bottom: 9pr;
        left: 50%;
        display: flex;
        height: 92pr;
        padding: 27pr 51pr;
        font-size: 39pr;
        line-height: 1;
        text-align: center;
        color: #38f;
        border-radius: 100pr;
        background-color: #ecf4ff;
        transform: translateX(-50%);

        .msg-text {
            overflow: hidden;
            max-width: 500pr;
            max-width: 40vw;
            margin-left: 26pr;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .arrow {
            position: relative;
            top: 6pr;
            width: 24pr;
            height: 28pr;
            background: url(./img/arr.png) 0 0 no-repeat;
            background-size: 24pr 28pr;
        }

        &::after {
            content: '';
            height: 293% !important;

            .border-common-color(#38f, 100pr, all);
        }
    }

    .has-ai-helper {
        height: 80% !important;
    }

    .inner-wrapper {
        width: 100%;
        height: 100%;
        overflow: scroll;
        -webkit-overflow-scrolling: touch;

        &::-webkit-scrollbar {
            display: none;
            width: 0;
            height: 0;
        }

        .chat-list {
            min-height: 100%;
            margin-top: 150px;
        }

        .chat-item-container {
            display: flex;
            align-items: center;
            margin-bottom: 12pr;

            .chat-item {
                padding: 12pr 24pr;
                font-weight: bold;
                font-size: 42pr;
                line-height: 66pr;
                .bgMask(.23, 42pr);

                .is-android & {
                    font-weight: normal;
                }

                .user-name {
                    color: #FFE187;
                }

                .msg {
                    text-shadow: 0 0 .5px rgba(0, 0, 0, .3);
                }

                .msg-hint {
                    opacity: .6;
                }
            }
        }
    }
}

.ad-chat-msg-wrapper-cny {
    height: 20vh;
}

.ai-helper-entry {
    width: 100%;
    height: 40px;
    border-radius: 14px;
    display: flex;
    align-items: center;
    padding: 12pr 24pr;
    font-weight: bold;
    font-size: 42pr;
    line-height: 66pr;
    .bgMask(.23, 42pr);

    .left-container {
        width: 70%;
        margin-right: 10px;

        .content {
            // 不换行，超出横向滚动
            white-space: nowrap;
            overflow: hidden;
            position: relative;

            &::-webkit-scrollbar {
                display: none;
                width: 0;
                height: 0;
            }

            // 当需要滚动时应用动画
            &.scroll-text {
                .text-content {
                    display: inline-block;
                    animation: horizontalScrollWithPause 8s linear infinite;

                    &:nth-child(2) {
                        margin-left: 20px; // 两个文本之间的间距
                    }
                }
            }

            .text-content {
                display: inline-block;
            }
        }
    }

    .right-button {
        width: 30%;
        font-weight: 500;
        font-size: 12px;
        color: #F35;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100px;
        padding-left: 10pr;
        padding-right: 15pr;

        .icon-play {
            width: 16px;
            height: 16px;
            background-image: url(./img/play.svg);
            background-repeat: no-repeat;
            background-position: center center;
        }
    }
}

// 横向滚动动画 - 带停顿效果
// 总时长8s: 初始停留1s + 滚动4s + 结束停留3s
@keyframes horizontalScrollWithPause {
    // 0-12.5%: 初始停留1s (1s/8s = 12.5%)
    0%,
    12.5% {
        transform: translateX(0);
    }

    // 12.5%-62.5%: 滚动4s (4s/8s = 50%, 12.5% + 50% = 62.5%)
    // 向左滚动100%加上20px的间距
    62.5% {
        transform: translateX(calc(-100% - 20px));
    }

    // 62.5%-100%: 滚动完成后停留3s (3s/8s = 37.5%, 62.5% + 37.5% = 100%)
    100% {
        transform: translateX(calc(-100% - 20px));
    }
}
