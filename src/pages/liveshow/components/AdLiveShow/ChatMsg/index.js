import './index.less';
import cls from 'classnames';
import React, {useEffect, useRef, useState} from 'react';
import Chat, {MessageType} from '@/pages/liveshow/utils/chat';
import emoticon from '@/pages/liveshow/utils/emoticon';
import {tinyScrollTo} from '@/utils/animation';
import {useAdLiveShowContext} from '../context';
import {onHelperClick} from '../utils/business';
import {useAiHelperTips} from '../hooks/useAiHelperTips';
import {outBubbleShowLog} from '../monitor/helper';

// 初始化加载im消息 1初始化 2im区域正在滚动 0初始化动画完成
let isInitScroll = 1;
// 是否展示悬浮im消息提示
let isShowLastMsg = false;

const showName = (name, hideName) => {
    if (!name) {
        return name;
    }

    if (hideName) {
        return `${name.slice(0, 1)}***`;
    }
    return name;
};

function ChatItem({item}) {
    let {username, content} = item;

    let props = {
        dangerouslySetInnerHTML: {
            __html: emoticon(content)
        }
    };

    return (
        <div className='chat-item-container'>
            <div className='chat-item'>
                {!!username && <span className='user-name'>{username}：</span>}
                <span className='msg' {...props} />
            </div>
        </div>
    );
}

function ChatMsg(props) {
    const {status, msgHlsUrl, hideName, chat_mcast_id, isLogin, isCNY, likeAction, chatList, setChatList} = props;
    const $msgBox = useRef();
    const $msgWrapper = useRef();
    const $chatList = useRef();
    const chat = useRef(Chat.getInstance());
    const [lastMsg, setLastMsg] = useState('');

    useEffect(() => {
        isInitScroll = 1;
        isShowLastMsg = false;

        initChat();
    }, []);

    useEffect(() => {
        if (!chatList.length) {
            return;
        }

        // 当前展示位置非im底部 悬浮提示最新消息
        if (isShowLastMsg) {
            let newMsg = chatList[chatList.length - 1];
            if (newMsg && newMsg.content) {
                setLastMsg(newMsg.content);
            }
            return;
        }

        // 正在执行初始化滚动 禁止其他操作
        if (isInitScroll === 2) {
            return;
        }

        // 当前展示位置是im底部 直接滚动展示新消息
        scrollToBottom(isInitScroll ? 'shake' : '');

        // 仅第一次加载im消息时执行
        if (isInitScroll) {
            isInitScroll = 2;
            initAddEvent();
        }
    }, [chatList]);
    const initChat = () => {
        chat.current.start({
            m3u8Link: msgHlsUrl,
            status: status,
            addDefaultMsg: true,
            chatID: chat_mcast_id,
            isLogin
        });

        chat.current.on(MessageType.MESSAGE, list => {
            setChatList(list.map(item => ({
                msgId: item.msgId,
                avatarSrc: item.portrait,
                username: showName(item.name, hideName),
                publishTime: item.timestr,
                content: item.word,
                responseUser: item.responseUser
            })));
        });
    };
    useEffect(() => {
        if (likeAction) {
            const newLikeMessage = {
                msgId: `like-${Date.now()}`,
                username: '',
                content: '你点赞了直播间',
                avatarSrc: '',
                publishTime: new Date().toISOString(),
                responseUser: null
            };
            setChatList(prev => [...prev, newLikeMessage]);
        }
    }, [likeAction]);
    const scrollToBottom = type => {
        const element = $msgBox.current;
        isShowLastMsg = false;
        setLastMsg('');

        if (element) {
            const {scrollHeight, clientHeight} = element;
            const scrollDistance = scrollHeight - clientHeight;

            if (type === 'shake') {
                // 初始化滚动
                const duration = Math.min(2000, 100 * Math.max(4, chatList.length));
                tinyScrollTo(element, 'scrollTop', scrollDistance, duration);

                setTimeout(() => {
                    if ($chatList.current) {
                        $chatList.current.style.marginTop = 0;
                    }
                    isInitScroll = 0;
                }, duration + 50);
                return;
            }

            // 非初始化滚动效果
            tinyScrollTo(element, 'scrollTop', scrollDistance, 200);
        }
    };

    const initAddEvent = () => {
        const element = $msgWrapper.current;
        if (element) {
            let startY = -1;
            let endY = -1;

            element.addEventListener('touchstart', e => {
                e.stopPropagation();

                if (e.touches[0]) {
                    startY = e.touches[0].screenY;
                }
            }, {passive: false});

            element.addEventListener('touchmove', e => {
                e.stopPropagation();

                if (e.touches[0]) {
                    endY = e.touches[0].screenY;
                }

                const isPull = startY < endY;
                const {scrollHeight, clientHeight, scrollTop} = $msgBox.current;
                const scrollDistance = scrollHeight - clientHeight;

                if (isPull && scrollTop === 0
                    || !isPull && scrollTop === scrollDistance) {
                    e.preventDefault();
                }
            }, {passive: false});

            element.addEventListener('touchend', e => {
                e.stopPropagation();

                const {scrollHeight, clientHeight, scrollTop} = $msgBox.current;
                if (scrollTop + 10 >= scrollHeight - clientHeight) {
                    isShowLastMsg = false;
                    setLastMsg('');
                }
                else {
                    isShowLastMsg = true;
                }
            }, {passive: false});
        }
    };
    const {anydoorOptimized, setAiHelperDrawVisible, setIsPlay} = useAdLiveShowContext();
    const {
        aiHelperTipsVisible,
        shouldScroll,
        currrentVideoTitle,
        contentRef,
        currrentVideoID
    } = useAiHelperTips();
    useEffect(() => {
        if (aiHelperTipsVisible) {
            outBubbleShowLog({
                from: 'h5',
                videoId: currrentVideoID
            });
        }
    }, [aiHelperTipsVisible, currrentVideoID]);
    return (
        <div
            className={cls('ad-chat-msg-wrapper', {
                'ad-chat-msg-wrapper-cny': isCNY
            })}
            ref={$msgWrapper}
        >
            <div
                className={cls('inner-wrapper', {
                    'has-ai-helper': aiHelperTipsVisible
                })}
                ref={$msgBox}
            >
                <div
                    className="chat-list"
                    ref={$chatList}
                >
                    {
                        chatList.map((item, idx) => (
                            <ChatItem key={item.msgId || idx} item={item} />
                        ))
                    }
                </div>
            </div>

            {/* 新消息提示 点击滚动到im底部 */}
            {lastMsg && (
                <div
                    className='last-msg'
                    onClick={scrollToBottom}
                >
                    <div className='arrow' />
                    <div className='msg-text'>{lastMsg}</div>
                </div>
            )}
            {aiHelperTipsVisible && <div
                className="ai-helper-entry"
                onClick={() => {
                    onHelperClick({
                        anydoorOptimized,
                        setAiHelperDrawVisible,
                        currrentVideoID
                    });
                    setIsPlay(false);
                }}
            >
                <div className="left-container">
                    <div
                        className={cls('content', {
                            'scroll-text': shouldScroll
                        })}
                        ref={contentRef}
                    >
                        <span className='text-content'>{currrentVideoTitle}</span>
                        {shouldScroll && <span className='text-content'>{currrentVideoTitle}</span>}
                    </div>
                </div>
                <div
                    className="right-button"
                    onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        onHelperClick({
                            anydoorOptimized,
                            setAiHelperDrawVisible,
                            currrentVideoID
                        });
                        setIsPlay(true);
                    }}
                >
                    <div className="icon-play"></div>
                    <span className='play-text'>看回放</span>
                </div>
            </div>}
        </div>
    );
}

export default ChatMsg;
