/* eslint-disable max-len */
/*
 * @file 打点入口
 * <AUTHOR>
 * @date 2025-01-02 11:42:46
 */

import {getUrlParam} from '@/utils';
import UBC from '@/utils/ubc';
import {isAndroid, isIOS, UA} from '@baidu/boxx/env';
import {env} from '@/utils/env';
import {getUrlAllParam} from '../../../utils/getUrlParam';
import {getGuid, getPvid} from '../AnyDoor/request';
import {prerenderExp} from './abtest';

// 每次进页面都生成一个
const pvid = getPvid();

const appSource = getUrlParam('app_source');
const roomId = getUrlParam('room_id') || getUrlParam('roomid');
const pageStatus = {
    '-1': 'preview',
    0: 'live',
    2: 'live_end',
    3: 'record',
    4: 'h5_subscribe'
};

const isHarmony = /ArkWeb\//i.test(UA);
let os_system = 'other';
if (isAndroid) {
    os_system = 'android';
}
else if (isIOS) {
    os_system = 'ios';
}
else if (isHarmony) {
    os_system = 'harmony';
}
const ubc = new UBC({
    queryParams: {
        appname: 'baiduboxapp'
    }
});
const adUbcLog = (params = {}, ext = {}, serverId = '', isTiming = false) => {
    const urlParams = getUrlAllParam(location.href);
    const {source} = urlParams || {};
    try {
        const res = {
            serverId: serverId,
            config: {
                source,
                from: 'liveshow',
                ext: {
                    roomid: roomId,
                    // 取url里的app_source，代表上游分享来源的端，没有则为空
                    app_source: appSource,
                    os_system,
                    browser: env.isWechat ? 'wechat' : 'other',
                    ...ext
                },
                ...params
            }
        };

        ubc.send(res, isTiming);
    }
    catch (err) {
        // console.log(err);
    }
};
export default adUbcLog;
// 商业广告商业广告h5直播落地页 通用展示打点
export const adCommonShowLog = ({
    page, type, value, nid, screen, serverId, isNewVersion = true, pos = '',
    action, text, appToAnyDoorPerformance, timingApi, anyMountCardUrlH5 = '',
    userstat = '', from = '', isLogin = '', getLiveInfoTiming = '', videoId = '',
    index = '', questionId = '', duration
}) => {
    const params = new URLSearchParams(window.location.search);
    const jsonParams = Object.fromEntries(params.entries());
    const {
        bd_vid = '',
        room_id = '',
        bd_bxst = ''
    } = jsonParams || {};
    adUbcLog({
        page: pageStatus[page],
        type,
        value
    }, {
        nid,
        // 1竖全屏 2横半屏
        screen: String(+screen + 1),
        is_new_version: isNewVersion ? '1' : '0',
        pos,
        bd_vid,
        url_params: JSON.stringify(jsonParams),
        authorid: '',
        schemeExtLog: JSON.stringify({
            bd_vid, room_id, bd_bxst, userstat, action, text, cuid: getGuid(), pvid, appToAnyDoorPerformance,
            timingApi, anyMountCardUrlH5, from, isLogin, inPCTest: window.inPCTest, inNaTest: window.globalJumpTestValue,
            prerenderExp, getLiveInfoTiming, videoId, index, questionId, duration
        })
    }, serverId);
};

// 商业广告商业广告h5直播落地页 通用点击打点
export const adCommonClickLog = ({
    page, value, nid, screen,
    serverId, isNewVersion = true, pos = '', action = '', anyMountCardUrlType, launchRes,
    text = '', anyMountCardUrlH5 = '', userstat = '', from = '', type = 'click'
}) => {
    const params = new URLSearchParams(window.location.search);
    const jsonParams = Object.fromEntries(params.entries());
    const {
        bd_vid = '',
        room_id = '',
        bd_bxst = ''
    } = jsonParams || {};
    adUbcLog({
        page: pageStatus[page],
        type,
        value
    }, {
        nid,
        // 1竖全屏 2横半屏
        screen: String(+screen + 1),
        is_new_version: isNewVersion ? '1' : '0',
        pos,
        bd_vid,
        url_params: JSON.stringify(jsonParams),
        authorid: '',
        // 弹窗点击和任意门点击都复用一个UBC和百度统计打点，但是要再传一个额外标识，能区分是从哪个地方点的（播放中任意门、播放前任意门、引导弹窗）
        schemeExtLog: JSON.stringify({
            bd_vid, room_id, bd_bxst, action, text, cuid: getGuid(), pvid,
            anyMountCardUrlH5, anyMountCardUrlType, launchRes,
            userstat, from, inPCTest: window.inPCTest, inNaTest: window.globalJumpTestValue,
            prerenderExp
        })
    }, serverId);
};
// 商业广告商业广告h5直播落地页 通用时长开始打点
export const adCommonStartTimingLog = (page, value, nid, screen, serverId, isNewVersion, userstat) => {
    const params = new URLSearchParams(window.location.search);
    const jsonParams = Object.fromEntries(params.entries());
    const {
        bd_vid = '',
        room_id = '',
        bd_bxst = ''
    } = jsonParams || {};
    adUbcLog({
        // live_end(直播结束页) live record(回放页)
        page: pageStatus[page],
        type: 'timing',
        value
    }, {
        nid,
        // 接口返回screen为0(竖屏)和1(横屏)；打点传递得值为 1竖全屏 2横半屏
        screen: String(+screen + 1),
        is_new_version: isNewVersion ? '1' : '0',
        bd_vid,
        url_params: JSON.stringify(jsonParams),
        authorid: '',
        schemeExtLog: JSON.stringify({bd_vid, room_id, bd_bxst, userstat, cuid: getGuid(), pvid})
    }, serverId, true);
};

// 商业广告商业广告h5直播落地页 通用时长结束打点
export const adCommonEndTimingLog = (serverId) => {
    ubc.endTimingLog(serverId, false);
};
