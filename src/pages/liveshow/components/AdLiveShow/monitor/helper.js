/*
 * @file 小助手需求埋点
 * <AUTHOR>
 * @date 2025-08-05 20:41:55
 */

import {adCommonShowLog} from './ad';

function createHelperLog({serverId}) {
    return (params = {}) => adCommonShowLog({
        page: 'helper',
        type: 'enter',
        value: 'entry',
        serverId,
        ...params
    });
}

/**
 * 摘要面板展现日志上报函数
 *
 * @function
 * @param {Object} params - 参数对象
 * @param {string} params.videoId - 视频 ID，用于标识当前展示的内容
 * @param {string} params.from - 来源标识，用于区分不同来源（如 H5 或原生）
 */
export const showSummaryPanelLog = createHelperLog({
    serverId: '19672'
});

/**
 * 摘要面板顶部 tab 点击日志上报
 * @param {Object} params
 * @param {string} params.videoId - 视频 ID
 * @param {number} params.index - tab 第几位
 * @param {string} params.from - 来源标识，用于区分不同来源（如 H5 或原生）
 */
export const topTabClickLog = createHelperLog({
    serverId: '19671'
});

/**
 * 视频区域点击日志上报
 * @param {Object} params
 * @param {string} params.videoId - 视频 ID
 * @param {string} params.from - 来源标识，用于区分不同来源（如 H5 或原生）
 */
export const videoAreaClickLog = createHelperLog({
    serverId: '19670'
});

/**
 * 猜你想问点击发送日志上报
 * @param {Object} params
 * @param {string} params.videoId - 视频 ID
 * @param {string|number} params.questionId - 问题 ID
 * @param {string} params.from - 来源标识，用于区分不同来源（如 H5 或原生）
 */
export const guessQuestionClickLog = createHelperLog({
    serverId: '19668'
});

/**
 * 自定义问题按钮点击日志上报
 * @param {Object} params
 * @param {string} params.videoId - 视频 ID
 * @param {string} params.from - 来源标识，用于区分不同来源（如 H5 或原生）
 */
export const customQuestionClickLog = createHelperLog({
    serverId: '19666'
});

/**
 * 摘要面板底部转化组件点击日志上报 19091
 * @param {Object} params
 * @param {string} params.position - 点击位置描述（如是否按钮）
 */
// 复用 19091

/**
 * 小助手气泡点击（线索直播端外）日志上报
 * @param {Object} params
 * @param {string} params.videoId - 视频 ID
 * @param {string} params.from - 来源标识，用于区分不同来源（如 H5 或原生）
 */
export const outBubbleClickLog = createHelperLog({
    serverId: '19669'
});

/**
 * 小助手气泡展现（线索直播端外）日志上报
 * @param {Object} params
 * @param {string} params.videoId - 视频 ID
 * @param {string} params.from - 来源标识，用于区分不同来源（如 H5 或原生）
 */
export const outBubbleShowLog = createHelperLog({
    serverId: '19665'
});

/**
 * 小助手底 bar 展现（线索直播端外）日志上报
 * @param {string} params.from - 来源标识，用于区分不同来源（如 H5 或原生）
 */
export const outBottomBarShowLog = createHelperLog({
    serverId: '19674'
});

/**
 * 小助手底 bar 点击（线索直播端外）日志上报
 * @param {string} params.from - 来源标识，用于区分不同来源（如 H5 或原生）
 */
export const outBottomBarClickLog = createHelperLog({
    serverId: '19675'
});

/**
 * 视频播放时间日志上报（每 5 秒一次）
 * @param {Object} params
 * @param {string} params.videoId - 视频 ID
 * @param {string} params.from - 来源标识，用于区分不同来源（如 H5 或原生）
 */
export const videoPlayTimeLog = createHelperLog({
    serverId: '19673'
});
