/*
 * @file 商业直播组件
 * <AUTHOR>
 * @date 2025-01-10 17:03:19
 */
import React, {useEffect, useState, useRef} from 'react';
import {execInvoke} from '@baidu/mcp-sdk';
import './index.less';
import './tomas.less';
import cls from 'classnames';
import ignoreReflow from '@/pages/liveshow/utils/ignoreReflow';
import {env} from '@/utils/env';
import {registerPageHidden} from '@/utils/index';
import {
    LiveStatus,
    invokeKeys,
    supportMcpInvoke
} from '@/pages/liveshow/config/const';
import {PageStore, isFromHonorBrowser} from '@/pages/liveshow/service/register';
import {watchOrientation} from '@/utils/scrollApprove';
import {getAppSchemeHead, getUrlAllParam} from '@/pages/liveshow/utils/getUrlParam';
import {matrixAppSchemeHeaderList, reflow} from '@/pages/liveshow/utils/reflow';
import {commonShowLog, commonStartTimingLog, commonEndTimingLog} from '@/pages/liveshow/log';
import {useRequest} from 'ahooks';
import {useABTest} from '@/abtest-sdk';
import {isWeChatUrl, wxLaunchImpl} from '@/utils/wxLaunch';
import {weirwood} from '../../utils/errorMonitor';
import {useIframe} from './hooks/useIframe';
import {useDirectOpenUrl} from './hooks/useDirectOpenUrl';
import IframeDoor from './AnyDoor/IframeDoor';
import AnyDoor from './AnyDoor';
import LiveVideo from './LiveVideo';
import LiveRoomHead from './LiveRoomHead';
import ChatMsg from './ChatMsg';
import {requestRoomEnterInfoWithAntiCheats} from './AnyDoor/api';
import {adCommonClickLog, adCommonEndTimingLog, adCommonShowLog, adCommonStartTimingLog} from './monitor/ad';
import {shouldAnydoorOptimize} from './monitor/abtest';
import LiveRoomClosed from './LiveRoomClosed';
import {getPeopleText, getTextByTransType, showAnyDoor} from './utils/business';
import ChatBar from './ChatBar';
import {sendMonitor} from './monitor';
import {baiduMonitorUa} from './AnyDoor/utils';
import {AdLiveShowProvider} from './context';


// 关于tag=baijiahao，因为是很久以前的逻辑，pm和开发人员已经不在了，所以只能保留原有逻辑
// 1. 在百家号APP上，做了一个视频切片，展示特等时间区间的直播
// 2. tag=baijiahao 且 直播结束有回放时，只展示视频播放器
// 3. tag=baijiahao时，不会展示付费等提示信息
const isBaijiahaoTag = PageStore.queryParams.tag === 'baijiahao';
const isAutoPlay = +PageStore.queryParams.auto_play === 1;
const appSchemeHead = getAppSchemeHead();
// 回流矩阵场景 || iOS Lite H5 页内观看
const isMatrixApp = matrixAppSchemeHeaderList.includes(appSchemeHead) || (env.isIOS && env.isLiteBox);
// 不执行回流 / 不创建微信开放标签 / 不展示5s回流弹窗
const noreflow = PageStore.queryParams.noreflow === 'all';

let isLiveEndNoReview = false;
// 当前屏幕 1横屏 2竖屏（默认）
let curBrowserScreen = 2;
// 当前页面 1播放器页 2推荐列表页
let curPage = 1;
// touchstart screenY位置
let startY = -1;
// touchend screenY位置
let endY = -1;

// 调起位信息（mcp内配置API返回）此处因为在触发播放器事件后，导致状态信息清空
let invokeInfo = {};


function AdLiveShow({data, refresh}) {
    const {
        status,
        screen,
        nid,
        has_recommend,
        background_url,
        coverImg,
        room_flag, // cny直播间
        concert_cny_config = {}, // cny云控内容
        share = {}
    } = data;
    const mainTitle = share.main_title || '';
    const shareUrl = share.share_url || '';
    const [chatList, setChatList] = useState([]);
    // 直播结束没有回放
    isLiveEndNoReview = status === LiveStatus.END_NO_REVIEW;
    const isCNY = !!room_flag && room_flag === 'concert_cny_new';
    // 是否展示推荐列表
    const showRecommend = !!+has_recommend && !ignoreReflow; // 更多推荐入口和列表展示逻辑统一
    const showRecMoreBtn = showRecommend && !isFromHonorBrowser && !isCNY;
    const [recommendFirstShow, setRecommendFirstShow] = useState(false);
    // 初始化 视频未播放之前
    const [isInitStatus, setInitStatus] = useState(true);
    // 封面链接
    const [backgroundUrl] = useState(background_url || coverImg);
    const showNewBackground = background_url && +screen === 1 || isCNY;
    const getRootClass = cls({
        'white-screen-patrol': true,
        'ad-live-wrapper': true,
        [appSchemeHead]: true,
        'is-hor-video': +screen === 1,
        'is-living-video': status === LiveStatus.LIVEING,
        'is-end-no-review': isLiveEndNoReview,
        'no-recommend-list': !showRecommend,
        'is-android': env.isAndroid,
        'is-cny': isCNY
    });

    const {userstat} = useABTest();

    useEffect(() => {
        watchOrientation((res) => {
            curBrowserScreen = res;
            if (!recommendFirstShow && res === 1) {
                setRecommendFirstShow(true);
            }
        });

        registerPageHidden(
            () => {
                // timing类日志上报SDK需要保证状态一致，不能存在userstat从''->'xxx'，这样会只能发送前一种状态
                userstat && adCommonEndTimingLog('19090');
                commonEndTimingLog('15266');
            },
            () => {
                userstat && adCommonStartTimingLog(status, 'timing', nid, screen, '19090', true, userstat);
                commonStartTimingLog(status, '', nid, screen, '15266', true);
            }
        );
    }, [userstat]);
    useEffect(() => {
        if (window.prefetchError) {
            weirwood.error.captureException(window.prefetchError);
        }
        if (window.prefetchsearchBoxError) {
            weirwood.error.captureException(window.prefetchsearchBoxError);
        }
    }, []);
    useEffect(() => {
        adCommonShowLog({
            page: status, type: 'enter', value: 'entry', nid, screen, serverId: '19092', isNewVersion: true,
            userstat
        });
        userstat && adCommonStartTimingLog(status, 'timing', nid, screen, '19090', true, userstat);

        commonShowLog(status, '', nid, screen, '15267', true);
        commonStartTimingLog(status, '', nid, screen, '15266', true);
        // sendMonitor('click', {
        //     level: 'live-show', info: baiduMonitorUa, value: status,
        //     bd_vid, root: room_id, livesource, source, ch
        // });
    }, [status, userstat]);
    // 执行具体调起行为
    const execMcpInvoke = async (key) => {

        // 支持url参数判断不执行回流
        if (noreflow) {
            return;
        }

        if (invokeKeys.indexOf(key) === -1) {
            window.console.log('该调起位未登记,请先行登记');
            // 执行默认调起行为
            return;
        }
        const invokeInfoForPos = invokeInfo && invokeInfo.action_rule && invokeInfo.action_rule[`pos_${key}`];

        if (!invokeInfoForPos || !Array.isArray(invokeInfoForPos) || !invokeInfoForPos[0]) {
            window.console.log('无相关调起位信息');
            // 执行默认调起 兼容接口返回异常情况
            reflow({roomId: PageStore.roomId});
            return;
        }
        try {
            const res = await execInvoke(
                `pos_${key}`,
                invokeInfoForPos[0],
                invokeInfoForPos[0].log_id
            );
            // 调起失败后新增调起兜底行为
            if ([10000, 10001, 10002, 10003].indexOf(res.status) === -1) {
                // 默认调起行为
                reflow({roomId: PageStore.roomId});
            }
            window.console.log(res.msg);
        }
        catch (e) {
            window.console.log(e);
            // 执行默认调起行为
            reflow({roomId: PageStore.roomId});
        }
    };

    const onClickShowModel = () => {
        if (env.isMainBox) {
            return;
        }
    };

    const [isShowDoor, setIsShowDoor] = useState(false);
    const {showChatTab} = data;
    const $swiperWrapper = useRef();
    const $innerScroll = useRef();
    curPage = isLiveEndNoReview ? 2 : 1;
    if (!recommendFirstShow && curPage === 2) {
        setRecommendFirstShow(true);
    }

    useEffect(() => {
        initAddEvent();
    }, []);

    // 切换页面
    const swiperScreen = value => {
        curPage = value;
        if (!recommendFirstShow && value === 2) {
            setRecommendFirstShow(true);
        }

        const swiperDom = $swiperWrapper.current;
        swiperDom.style.transform = `translate(0px, -${swiperDom.clientHeight * (value - 1)}px)`;
    };

    const initAddEvent = () => {
        const element = $swiperWrapper.current;
        ['touchstart', 'touchend', 'touchmove'].forEach(item => {
            element.addEventListener(item, (e) => handleTouchEvent(e, item), {passive: false});
        });
    };
    const str = 'x-bce-process';
    function countExactMatch(source, target) {
        const regex = new RegExp(target.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'); // 转义正则特殊字符
        return (source.match(regex) || []).length;
    }

    useEffect(() => {
        const vertical_cover = data?.video?.cover?.vertical_cover || '';
        const cover_100 = data?.video?.cover?.cover_100 || '';
        const vertical_cover_count = countExactMatch(vertical_cover, str);
        const cover_100_count = countExactMatch(cover_100, str);
        if (vertical_cover_count > 1) {
            sendMonitor('click', {
                item: vertical_cover_count,
                level: 'vertical_cover_count'
            });
        }

        if (cover_100_count > 1) {
            sendMonitor('click', {
                item: cover_100_count,
                level: 'cover_100_count'
            });
        }
    }, [data]);
    const handleTouchEvent = (e, eventType) => {

        // 当前屏幕为横屏 或者 只有一屏的时候不切换页面
        if (curBrowserScreen === 1 || isLiveEndNoReview) {
            return;
        }

        // 点击的是播放器controls
        const playerBottom = document.querySelector('#video-box .mp-bottom');
        if (playerBottom && playerBottom.contains(e.target)) {
            return;
        }

        // 点击的是消息区
        const msg = document.querySelector('.chat-msg-wrapper');
        if (msg && msg.contains(e.target)) {
            return;
        }

        e.stopPropagation();
        const element = $innerScroll.current;

        if (element) {
            const {scrollTop} = element;

            if (eventType === 'touchstart') {
                // 如果当前是第二屏且页面没有滚动到顶部 不切换页面
                if (curPage === 2 && scrollTop > 0) {
                    return;
                }

                if (e.touches[0]) {
                    startY = e.touches[0].screenY;
                }

                return;
            }

            if (eventType === 'touchmove') {
                if (e.touches[0]) {
                    endY = e.touches[0].screenY;
                }

                // 阻止浏览器默认事件
                const isPull = startY < endY;
                const needCancel = curPage === 1
                    || curPage === 2 && isPull && scrollTop === 0;

                if (e.cancelable && needCancel) {
                    e.preventDefault();
                }

                return;
            }
        }


        if (eventType === 'touchend') {
            // 直播结束没有回放
            if (isLiveEndNoReview) {
                return;
            }

            if (startY > -1 && endY > -1) {
                const distance = startY - endY;
                if (Math.abs(distance) > 100) {
                    if (distance > 0) {
                        swiperScreen(2);
                    }
                    else {
                        swiperScreen(1);
                    }
                }
            }

            startY = -1;
            endY = -1;
            return;
        }
    };

    const handleInvoke = key => {
        // 判断仅在支持MCP时，统一执行调起
        if (!supportMcpInvoke) {
            return;
        }

        execMcpInvoke(key);
    };
    const {data: {
        anyMountResult = {},
        dynamicPeoplesResult = {},
        roomVideo = {}
    } = {}} = useRequest(requestRoomEnterInfoWithAntiCheats, {
        defaultParams: [PageStore.roomId, PageStore.deviceId]
    });
    const [hasClickShowModel, setHasClickShowModel] = useState(false);

    // 使用iframe hook
    const {
        showIframe,
        isClosing,
        iframeUrl,
        closeIframe
    } = useIframe(hasClickShowModel);
    const {solutionInfo, anyMountCardUrlType, anyMountCardUrlH5, shareUrlParams, transType = ''} = anyMountResult;
    // 使用直接打开URL的hook
    useDirectOpenUrl(isShowDoor, anyMountCardUrlType, anyMountCardUrlH5);
    const finalShareUrl = shareUrl + shareUrlParams;
    const urlParams = getUrlAllParam(location.href);
    const {
        bd_vid = '',
        ch = '',
        livesource = '',
        source = '',
        room_id
    } = urlParams || {};
    const handleDoorOpen = (from = 'card') => {
        if (!isWeChatUrl(anyMountCardUrlType)) {
            adCommonClickLog({
                page: status,
                value: 'trade_button',
                nid,
                screen,
                serverId: '19091',
                isNewVersion: true,
                action: `${hasClickShowModel ? 'inner' : 'out'}_card`,
                anyMountCardUrlH5,
                anyMountCardUrlType,
                userstat,
                from
            });
            sendMonitor('click', {
                level: 'any-door-open',
                info: baiduMonitorUa,
                value: status,
                target: hasClickShowModel,
                bd_vid, root: room_id, livesource, source, ch,
                action: `${hasClickShowModel ? 'inner' : 'out'}_card`
            });
            window.open(anyMountCardUrlH5);
            return;
        }
        wxLaunchImpl({
            solutionId: solutionInfo?.solutionId,
            solutionRelatedId: solutionInfo?.solutionRelatedId,
            h5Options: {
                status,
                nid,
                screen,
                anyMountCardUrlH5,
                anyMountCardUrlType,
                hasClickShowModel,
                isPlay
            }
        });

    };
    const anyDoorProps = {
        ...anyMountResult,
        ...dynamicPeoplesResult,
        setIsShowDoor,
        status,
        nid,
        screen,
        hasClickShowModel,
        handleDoorOpen
    };
    const showDoorFromData = showAnyDoor(anyMountResult);
    useEffect(() => {
        if (showDoorFromData) {
            setIsShowDoor(true);
        }
        if (anyMountResult.anyMountSwitch === 0) {
            sendMonitor('click', {
                level: 'any_mount_switch_0',
                info: baiduMonitorUa,
                value: status,
                bd_vid, root: room_id,
                params: JSON.stringify(anyMountResult)
            });
        }
    }, [anyMountResult, bd_vid, room_id, showDoorFromData, status]);
    const inChatBarTest = hasClickShowModel;
    const [showInput, setShowInput] = useState(false);

    const [likeAction, setLikeAction] = useState(false);
    // 播放器模块
    const VideoScreen = () => {
        return (
            <div className="page-screen video-screen">
                {!isLiveEndNoReview && (
                    <React.Fragment>
                        {/* 播放器 & 暂停遮罩 */}
                        <LiveVideo
                            {...data}
                            isBaijiahaoTag={isBaijiahaoTag}
                            updatePageData={refresh}
                            onClickShowModel={onClickShowModel}
                            setInitStatus={setInitStatus}
                            isInitStatus={isInitStatus}
                            isAutoPlay={isAutoPlay}
                            handleInvoke={handleInvoke}
                            isMatrixApp={isMatrixApp}
                            invokeInfo={invokeInfo}
                            isCNY={isCNY}
                            noreflow={noreflow}
                            setIsShowDoor={setIsShowDoor}
                            showDoorFromData={showDoorFromData}
                            setHasClickShowModel={setHasClickShowModel}
                            hasClickShowModel={hasClickShowModel}
                        />

                        {/* 头部信息模块 */}
                        <LiveRoomHead
                            {...data}
                            updatePageData={refresh}
                            onClickShowModel={onClickShowModel}
                            swiperScreen={swiperScreen}
                            showRecMoreBtn={showRecMoreBtn}
                            componentPos='1'
                            handleInvoke={() => {}}
                            invokeInfo={invokeInfo}
                            isMatrixApp={isMatrixApp}
                            isCNY={isCNY}
                        />
                        {/* im消息模块 此处优先滑动事件 */}
                        {!shouldAnydoorOptimize(hasClickShowModel) && <div className="im-door-flex-wrapper">
                            <div className={cls('im-wrapper', {
                                'im-wrapper-cny': isCNY,
                                'shot-width': isShowDoor
                            })}
                            >
                                {showChatTab && !isInitStatus && <ChatMsg
                                    isCNY={isCNY}
                                    {...data}
                                    likeAction={likeAction}
                                    chatList={chatList}
                                    setChatList={setChatList}
                                />}
                            </div>
                            {hasClickShowModel
                            && <div className={cls('bottom-bar-wrapper preview-chat-bar', {
                                'show-input-bottom-bar-wrapper': showInput
                            })}
                            >
                                <ChatBar
                                    status={status}
                                    nid={nid}
                                    screen={screen}
                                    handleDoorOpen={handleDoorOpen}
                                    userInfo={data?.user_info}
                                    showDoorFromData={showDoorFromData}
                                    setLikeAction={setLikeAction}
                                    setChatList={setChatList}
                                    showInput={showInput}
                                    setShowInput={setShowInput}
                                    mainTitle={mainTitle}
                                    finalShareUrl={finalShareUrl}
                                />
                            </div>}
                            {isShowDoor && <AnyDoor {...anyDoorProps} />}
                        </div>}
                    </React.Fragment>
                )}
            </div>
        );
    };
    const showSecondScreen = isLiveEndNoReview;
    // 推荐列表 & 关播模块
    const RecommendScreen = () => {
        return (
            <div className="page-screen recommend-screen">
                <div
                    ref={$innerScroll}
                    className="inner-scroll"
                >
                    {/* 关播模块 */}
                    {isLiveEndNoReview && (
                        <React.Fragment>
                            <LiveRoomClosed {...data} />
                        </React.Fragment>
                    )}
                </div>
            </div>
        );
    };
    const finalTransType = transType?.split(';')[0];
    const t1 = getTextByTransType(finalTransType);
    const t2 = getPeopleText(finalTransType, room_id, data?.online_users || 0);
    const [isPlay, setIsPlay] = useState(false);
    const [aiHelperDrawVisible, setAiHelperDrawVisible] = useState(false);
    const anydoorOptimized = shouldAnydoorOptimize(hasClickShowModel);
    const context = {
        textItems: [t1, t2],
        anydoorOptimized,
        roomVideo,
        aiHelperDrawVisible,
        setAiHelperDrawVisible,
        isPlay,
        setIsPlay
    };
    if (inChatBarTest) {
        return (
            <AdLiveShowProvider value={context}>
                <div style={{width: '100%', height: '100%'}}>
                    <div
                        className={getRootClass}
                        style={{
                            width: '100%', height: shouldAnydoorOptimize(hasClickShowModel) ? '68%' : '100%'
                        }}
                    >
                        {/* 背景图 */}
                        {
                            <div
                                className={cls({'screen-bg': !showNewBackground, 'screen-bg-new': showNewBackground})}
                                style={{backgroundImage: isCNY && concert_cny_config.background_cover
                                    ? `url(${concert_cny_config.background_cover})`
                                    : `url(${backgroundUrl})`}}
                            >
                            </div>
                        }

                        {/* 页面内容 */}
                        <div
                            className="screen-wrapper-container"
                            ref={$swiperWrapper}
                        >
                            {!isLiveEndNoReview && VideoScreen()}
                            {showSecondScreen && RecommendScreen()}
                        </div>
                    </div>
                    {shouldAnydoorOptimize(hasClickShowModel) && <div
                        style={{
                            height: '25%'
                        }}
                        className='im-door-flex-wrapper optimize-door-container'
                    >
                        <div className={cls('im-wrapper', {
                            'im-wrapper-cny': isCNY,
                            'shot-width': isShowDoor
                        })}
                        >
                            {showChatTab && !isInitStatus && <ChatMsg
                                isCNY={isCNY}
                                {...data}
                                likeAction={likeAction}
                                chatList={chatList}
                                setChatList={setChatList}
                            />}
                        </div>
                        {isShowDoor && <div style={{height: '80%'}} className='optimize-door-wrapper'>
                            <AnyDoor {...anyDoorProps} />
                        </div>}
                    </div>}
                    {shouldAnydoorOptimize(hasClickShowModel) && hasClickShowModel && <div
                        className={cls('bottom-bar-wrapper im-door-flex-wrapper optimize-door-container', {
                            'show-input-bottom-bar-wrapper': showInput
                        })}
                        style={{
                            height: showInput ? 'auto' : '7%',
                            backgroundColor: showInput ? '#fff' : 'grey'
                        }}
                    >
                        <ChatBar
                            onClickShowModel={onClickShowModel}
                            setShowTip={() => {}}
                            status={status}
                            nid={nid}
                            screen={screen}
                            isMatrixApp={isMatrixApp}
                            handleInvoke={handleInvoke}
                            userInfo={data?.user_info}
                            showDoorFromData={showDoorFromData}
                            handleDoorOpen={handleDoorOpen}
                            setLikeAction={setLikeAction}
                            setChatList={setChatList}
                            showInput={showInput}
                            setShowInput={setShowInput}
                            mainTitle={mainTitle}
                            finalShareUrl={finalShareUrl}
                        />
                    </div>}
                    <IframeDoor
                        showIframe={showIframe}
                        isClosing={isClosing}
                        iframeUrl={iframeUrl}
                        closeIframe={closeIframe}
                    />
                </div>
            </AdLiveShowProvider>
        );

    }
    return (
        <AdLiveShowProvider value={context}>
            <div style={{width: '100%', height: '100%'}}>
                <div
                    className={getRootClass}
                    style={{
                        width: '100%', height: shouldAnydoorOptimize(hasClickShowModel) ? '75%' : '100%'
                    }}
                >
                    {/* 背景图 */}
                    {
                        <div
                            className={cls({'screen-bg': !showNewBackground, 'screen-bg-new': showNewBackground})}
                            style={{backgroundImage: isCNY && concert_cny_config.background_cover
                                ? `url(${concert_cny_config.background_cover})`
                                : `url(${backgroundUrl})`}}
                        >
                        </div>
                    }

                    {/* 页面内容 */}
                    <div
                        className="screen-wrapper-container"
                        ref={$swiperWrapper}
                    >
                        {!isLiveEndNoReview && VideoScreen()}
                        {showSecondScreen && RecommendScreen()}
                    </div>
                </div>
                {shouldAnydoorOptimize(hasClickShowModel) && <div
                    style={{
                        height: '25%'
                    }}
                    className='im-door-flex-wrapper optimize-door-container'
                >
                    <div className={cls('im-wrapper', {
                        'im-wrapper-cny': isCNY,
                        'shot-width': isShowDoor
                    })}
                    >
                        {showChatTab && !isInitStatus && <ChatMsg
                            isCNY={isCNY}
                            {...data}
                            likeAction={likeAction}
                            chatList={chatList}
                            setChatList={setChatList}
                        />
                        }
                    </div>
                    {isShowDoor && <div style={{height: '80%'}} className='optimize-door-wrapper'>
                        <AnyDoor {...anyDoorProps} />
                    </div>}
                </div>}
                <IframeDoor
                    showIframe={showIframe}
                    isClosing={isClosing}
                    iframeUrl={iframeUrl}
                    closeIframe={closeIframe}
                />
            </div>
        </AdLiveShowProvider>

    );


}

export default AdLiveShow;
