/*
 * @file 业务工具方法
 * <AUTHOR>
 * @date 2025-03-12 12:05:49
 */

import {isWeChatUrl, wxLaunchImpl} from '@/utils/wxLaunch';
import {getUrlAllParam} from '@/pages/liveshow/utils/getUrlParam';
import {adCommonClickLog} from '../monitor/ad';
import {sendMonitor} from '../monitor';
import {baiduMonitorUa} from '../AnyDoor/utils';
import {outBubbleClickLog} from '../monitor/helper';

export function showAnyDoor(anyMountResult) {
    return JSON.stringify(anyMountResult) !== '{}' && anyMountResult.anyMountSwitch === 1;
}

export function getTextByTransType(transType) {
    const textMap = {
        132: '获取专业解答，点此咨询！',
        152: '获取专业解答，点此咨询！',
        139: '获取专业解答，点此咨询！',
        153: '精彩内容，点此下载！',
        133: '加主播微信，领取资料！',
        131: '品质保障，点击购买！'
    };

    return textMap[transType] || '更多精彩内容，点击预约！';
}

export function getPeopleText(transType, room_id, liveCount) {
    const baseTextMap = {
        132: '人咨询',
        152: '人咨询',
        139: '人咨询',
        153: '人下载',
        133: '人添加主播微信',
        131: '人购买'
    };

    const fallbackText = '人预约';

    let actionText = baseTextMap[transType] || fallbackText;

    let countText = '多';
    if (typeof room_id === 'string' && /^\d+$/.test(room_id)) {
        const idStr = String(room_id);
        if (liveCount >= 10000 && idStr.length >= 4) {
            countText = idStr.slice(-4);
        }
        else if (liveCount >= 1000 && idStr.length >= 3) {
            countText = idStr.slice(-3);
        }
        else if (liveCount >= 100 && idStr.length >= 2) {
            countText = idStr.slice(-2);
        }
    }

    return `已有${countText}${actionText}`;
}

/**
 * 页面级复用的任意门打开逻辑
 * @param {*} from
 * @param {*} param1
 * @returns
 */
export const handleDoorOpenShared = (from = 'ai_helper', {
    anyMountCardUrlType,
    anyMountCardUrlH5,
    solutionInfo,
    hasClickShowModel = true,
    status = 0,
    screen = 0,
    userstat = ''
}) => {
    const {livesource, bd_vid, room_id, source, ch, nid} = getUrlAllParam();
    if (!isWeChatUrl(anyMountCardUrlType)) {
        adCommonClickLog({
            page: status,
            value: 'trade_button',
            nid,
            screen,
            serverId: '19091',
            isNewVersion: true,
            action: `${hasClickShowModel ? 'inner' : 'out'}_card`,
            anyMountCardUrlH5,
            anyMountCardUrlType,
            userstat,
            from
        });
        sendMonitor('click', {
            level: 'any-door-open',
            info: baiduMonitorUa,
            value: status,
            target: hasClickShowModel,
            bd_vid, root: room_id, livesource, source, ch,
            action: `${hasClickShowModel ? 'inner' : 'out'}_card`
        });
        window.open(anyMountCardUrlH5);
        return;
    }
    wxLaunchImpl({
        solutionId: solutionInfo?.solutionId,
        solutionRelatedId: solutionInfo?.solutionRelatedId,
        h5Options: {
            status,
            nid,
            screen,
            anyMountCardUrlH5,
            anyMountCardUrlType,
            hasClickShowModel
        }
    });

};
export function onHelperClick({
    anydoorOptimized,
    setAiHelperDrawVisible,
    currrentVideoID
}) {
    outBubbleClickLog({
        from: 'h5',
        videoId: currrentVideoID
    });
    if (anydoorOptimized) {
        // 获取当前页面的url的中query内容，到达新页面地址后拼接上
        // 新页面路径：/m/media/clue/qingkuang/index.html
        const url = location.href;
        const urlParams = new URLSearchParams(url.split('?')[1]);
        // 拼接额外参数：anydoorOptimized
        const newUrl = `/m/media/clue/qingkuang/index.html?${urlParams.toString()}&anydoorOptimized=true`;
        window.location.href = newUrl;
    }
    else {
        setAiHelperDrawVisible(true);
    }
}