/*
 * @file 聊天框组件（新版）
 * <AUTHOR>
 * @date 2025-05-20 16:18:02
 */

import React, {useEffect, useState} from 'react';
import './index.less';
import {commonShowLog} from '@/pages/liveshow/log';
import {useABTest, useABTestValue} from '@/abtest-sdk';
import {withBoundary} from 'react-suspense-boundary';
import classNames from 'classnames';
import Popup from 'antd-mobile/es/components/popup';
import AIHelper from '@/components/AIHelper';
import {adCommonShowLog} from '../monitor/ad';
import {convEnhanceValueMap} from '../monitor/abtest';
import {useAdLiveShowContext} from '../context';
import {onHelperClick} from '../utils/business';
import {useAiHelperTips} from '../hooks/useAiHelperTips';
import ChatInputTrigger from './ChatInputTrigger';
import LikeButton from './LikeButton';
import SharePanel from './SharePanel';

// 胶囊组件动态导入策略映射
const CAPSULE_STRATEGIES = {
    expand: React.lazy(() => import('./components/ExpandCapsule')), // inConvEnhance3: 展开模式
    noIconCarousel: React.lazy(() => import('./components/NoIconCarouselCapsule')), // inConvEnhance2: 无图标轮播
    carousel: React.lazy(() => import('./components/CarouselCapsule')), // inConvEnhance1: 有图标轮播
    buttonType: React.lazy(() => import('./components/ButtonTypeCapsule')), // inConvEnhance4: 按钮类型
    buttonLeft: React.lazy(() => import('./components/ButtonLeftCarousel')), // inConvEnhance5: 按钮左对齐
    default: React.lazy(() => import('./components/DefaultCapsule')) // 默认模式: 只展示任意门
};

// 根据AB测试值获取策略类型
const getCapsuleStrategy = (convEnhanceFlags) => {
    const {inConvEnhance1, inConvEnhance2, inConvEnhance3, inConvEnhance4, inConvEnhance5} = convEnhanceFlags;

    if (inConvEnhance3) {
        return 'expand';
    }
    if (inConvEnhance2) {
        return 'noIconCarousel';
    }
    if (inConvEnhance1) {
        return 'carousel';
    }
    if (inConvEnhance4) {
        return 'buttonType';
    }
    if (inConvEnhance5) {
        return 'buttonLeft';
    }

    return 'default';
};

// 策略模式：根据AB测试渲染不同的胶囊组件
const renderCapsuleByStrategy = (convEnhanceFlags, onDoorOpen) => {
    const strategyType = getCapsuleStrategy(convEnhanceFlags);
    const CapsuleComponent = CAPSULE_STRATEGIES[strategyType];

    return (
        <CapsuleComponent onDoorOpen={onDoorOpen} />
    );
};

function ChatBar(props) {
    const convEnhanceValue = useABTestValue('convEnhance');
    const inConvEnhance1 = convEnhanceValue === convEnhanceValueMap.test1;
    const inConvEnhance2 = convEnhanceValue === convEnhanceValueMap.test2;
    const inConvEnhance3 = convEnhanceValue === convEnhanceValueMap.test3;
    const inConvEnhance4 = convEnhanceValue === convEnhanceValueMap.test4;
    const inConvEnhance5 = convEnhanceValue === convEnhanceValueMap.test5;
    const inConvEnhanceTest = inConvEnhance1 || inConvEnhance2 || inConvEnhance3 || inConvEnhance4 || inConvEnhance5;
    const {
        status, nid, screen, handleDoorOpen, userInfo, showDoorFromData, setChatList,
        showInput, setShowInput, mainTitle, finalShareUrl
    } = props;
    const [isSharePanelVisible, setIsSharePanelVisible] = useState(false);

    useEffect(() => {
        ['text_box', 'gift', 'share'].forEach((item) => {
            commonShowLog(status, item, nid, screen, '15262', true);
        });
    }, []);

    const clickBottomBar = e => {
        e.preventDefault();
        e.stopPropagation();

    };
    const {userstat} = useABTest();
    useEffect(() => {
        if (showDoorFromData) {
            adCommonShowLog({
                page: status,
                type: 'show',
                value: 'trade_recommend',
                nid,
                screen,
                serverId: '19091',
                isNewVersion: true,
                userstat,
                from: 'eye'
            });
        }
        // 利用19536 上报所有互动组件的展现
        adCommonShowLog({
            page: status,
            type: 'enter',
            value: 'entry',
            nid,
            screen,
            serverId: '19536',
            isNewVersion: true,
            userstat,
            action: 'interaction_show'
        });
    }, [showDoorFromData, nid, screen, status, userstat]);
    // 实验内 且不是实验④⑤ 展示聊天+分享
    const showChatInputOrShare = inConvEnhanceTest && !inConvEnhance4 && !inConvEnhance5;
    const {anydoorOptimized, aiHelperDrawVisible, setAiHelperDrawVisible, setIsPlay, isPlay} = useAdLiveShowContext();
    const {aiHelperTipsVisible, currrentVideoID} = useAiHelperTips();
    const h5Options = {
        aiHelperDrawVisible,
        handleDoorOpen,
        isPlay,
        videoId: currrentVideoID
    };
    return (
        <div
            className={classNames('chat-bar-wrapper', {
                'show-input-chat-bar-wrapper': showInput,
                'in-conv-enhance-test5': inConvEnhance5
            })}
            onClick={clickBottomBar}
        >
            {showChatInputOrShare && <ChatInputTrigger
                setShowInput={setShowInput}
                showInput={showInput}
                userInfo={userInfo}
                setChatList={setChatList}
                {...props}
            />}
            {(showChatInputOrShare) && !showInput
            && <div className="icon-2" onClick={() => setIsSharePanelVisible(true)}></div>}
            {aiHelperTipsVisible && <div
                className='ai-helper-icon-wrapper icon-3'
                onClick={() => {
                    onHelperClick({
                        anydoorOptimized,
                        setAiHelperDrawVisible,
                        currrentVideoID
                    });
                    setIsPlay(false);
                }}
            >
                {/* 暂时不用动态 */}
                {/* <LottieAnimation
                    type='ai-helper'
                    width={40}
                    height={40}
                    interval={500}
                    className="ai-helper-lottie-animation"
                /> */}
            </div>}
            {showDoorFromData && !showInput
             && renderCapsuleByStrategy(
                 {inConvEnhance1, inConvEnhance2, inConvEnhance3, inConvEnhance4, inConvEnhance5},
                 () => handleDoorOpen('eye')
             )}
            <SharePanel
                visible={isSharePanelVisible}
                onClose={() => setIsSharePanelVisible(false)}
                shareUrl={finalShareUrl}
                mainTitle={mainTitle || ''}
                {...props}
            />
            {false && !showInput && <LikeButton {...props} />}
            <Popup
                visible={aiHelperDrawVisible}
                onClose={() => setAiHelperDrawVisible(false)}
                onMaskClick={() => setAiHelperDrawVisible(false)}
                bodyStyle={{height: '80vh'}}
                closeIcon
            >
                <div style={{width: '100%', height: '100%'}}>
                    <AIHelper h5Options={h5Options} />
                </div>
            </Popup>
        </div>
    );
}

export default withBoundary({
    renderError() {
        return null;
    }
})(ChatBar);
