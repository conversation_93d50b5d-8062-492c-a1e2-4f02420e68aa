/* 全局安全区域适配 */
body {
    /* iOS < 11.2 兼容 */
    padding: constant(safe-area-inset-top) constant(safe-area-inset-right) constant(safe-area-inset-bottom) constant(safe-area-inset-left);

    /* iOS >= 11.2 */
    padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);

    /* Android 刘海屏适配 - 经验值适配 */
    @media screen and (max-width: 768px) {
        /* 检测是否为Android设备 */
        @supports not (padding: env(safe-area-inset-top)) {
            /* 如果不支持env函数，说明可能是Android设备，添加顶部安全距离 */
            padding-top: 24px; /* Android状态栏高度经验值 */
        }
    }
}

// 主题颜色变量定义
:root {
    // 日间模式颜色
    --qk-bg-color-light: #fff;
    --qk-text-color-light: #1e1f24;

    // 暗黑模式颜色
    --qk-bg-color-dark: #fff;
    --qk-text-color-dark: #fff;

    // 夜间模式颜色
    --qk-bg-color-night: #666;
    --qk-text-color-night: #666;
}

.qingkuang-app-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100vw;
    height: 100vh;
    font-size: 20px;
    background: var(--qk-bg-color-light);
    color: var(--qk-text-color-light);

    /* 确保内容在安全区域内 */
    box-sizing: border-box;

    /* 如果只需要适配底部小黑条（使用原生导航栏的情况） */

    /* 可以只使用下面的样式替代body的全局适配 */

    /*
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    */
}

// 主题样式
html.theme-dark .qingkuang-app-container {
    background: var(--qk-bg-color-dark);
    color: var(--qk-text-color-dark);
}

html.theme-night .qingkuang-app-container {
    background: var(--qk-bg-color-night);
    color: var(--qk-text-color-night);
}
