/*
 * @file 视频播放器
 * <AUTHOR>
 * @date 2025-07-28 16:54:13
 */

import React, {useEffect, useRef} from 'react';
import HPlayer from '@baidu/hplayer';
import cls from 'classnames';
import {aiHelperNativeUtils} from './utils';
import {videoPlayTimeLog} from '@/pages/liveshow/components/AdLiveShow/monitor/helper';

export default function VideoPlayer({index, section, isH5, aiHelperDrawVisible, isPlay = false}) {
    const {videoUrl, videoCover, videoId} = section;
    const player = useRef();

    const autoplay = isH5 && isPlay && index === 0;
    // 记录播放时长的ref
    const playStartTime = useRef(0);
    const totalPlayTime = useRef(0);
    const isPlaying = useRef(false);

    function onClick() {
        if (!isH5) {
            player.current.pause();
            aiHelperNativeUtils.openVideoPlayer(videoUrl, videoCover, videoId);
        }
    }

    // 上报播放时长
    const reportPlayTime = () => {
        if (totalPlayTime.current > 0) {
            videoPlayTimeLog({videoId, duration: totalPlayTime.current, from: isH5 ? 'h5' : 'native'});
            totalPlayTime.current = 0; // 重置计时
        }
    };

    useEffect(() => {
        const container = document.getElementById(`h-video-${index}`);
        if (!container) {
            return;
        }

        player.current = new HPlayer({
            container,
            video: {
                url: videoUrl,
                pic: videoCover,
                autoplay
            },
            fullscreen: 'browser',
            showFullScreen: isH5,
            speed: isH5,
            autoplay
        });

        autoplay && player.current.play();

        player.current.on('play', () => {
            if (!isPlaying.current) {
                playStartTime.current = Date.now();
                isPlaying.current = true;
            }
        });
        player.current.on('pause', () => {
            if (isPlaying.current) {
                totalPlayTime.current += Date.now() - playStartTime.current;
                isPlaying.current = false;
                reportPlayTime();
            }
        });
        player.current.on('ended', () => {
            if (isPlaying.current) {
                totalPlayTime.current += Date.now() - playStartTime.current;
                isPlaying.current = false;
                reportPlayTime();
            }
        });
    }, [autoplay, index, isH5, videoCover, videoId, videoUrl]);

    useEffect(() => {
        if (!aiHelperDrawVisible && player.current) {
            player.current.pause();
            reportPlayTime();
        }
    }, [aiHelperDrawVisible]);

    return (
        <div
            onClick={onClick}
            className={cls({'video-player-h5-native': !isH5})}
            id={`h-video-${index}`}
            style={{width: '100%', height: '100%'}}
        ></div>
    );
}
