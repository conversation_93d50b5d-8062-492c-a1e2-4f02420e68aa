/*
 * @file less工具函数
 * <AUTHOR>
 * @date 2021-11-09 23:00:28
 */

@import (reference) '~@baidu/nano-theme/index.less';

.text-ellipse(@line) {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: @line;
    overflow: hidden;
}

// 常用边框样式，颜色使用样式表颜色
.border-common-theme(@code, @radius, @direction) {
    .theme-border(@code);
    .border-common(@radius, @direction);
}

// 常用边框样式，颜色可使用css色值
.border-common-color(@color, @radius, @direction) {
    border-color: @color;
    .border-common(@radius, @direction);
}

// 常用边框样式，无颜色
.border-common(@radius, @direction) {
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    width: 300%;
    height: 300%;
    transform: scale(.333);
    transform-origin: 0 0;
    border-width: 1px;
    border-radius: @radius * 3;
    z-index: 1;
    .border-style-direction(@direction);
}

.border-style-direction(@direction) when (@direction =left) {
    border-style: none none none solid;
}

.border-style-direction(@direction) when (@direction =right) {
    border-style: none solid none none;
}

.border-style-direction(@direction) when (@direction =top) {
    border-style: solid none none none;
}

.border-style-direction(@direction) when (@direction =bottom) {
    border-style: none none solid none;
}

.border-style-direction(@direction) when (@direction =all) {
    border-style: solid;
}

.bgMask(@opacity, @radius) {
    background-color: rgba(0, 0, 0, @opacity);
    border-radius: @radius;
}

.arrow(@width) {
    width: @width;
    height: @width;
    border-top: 5pr solid;
    border-left: 5pr solid;
    border-color: #fff;
    transform: rotate(135deg) translateY(15pr);
}

.common-avatar(@width, @height) {
    position: relative;
    overflow: hidden;
    width: @width;
    height: @height;
    border: none;
    border-radius: 50%;
    display: flex;
}

.common-flex-center() {
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

.common-flex-center-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.common-btn-bgcolor {
    background: linear-gradient(180deg, #FF1F66 0%, #FF4D4D 100%);

    // xxMatrix 是预约样式  .baiduxxx 是落地页样式
    .haokanMatrix &,
    .baiduhaokan & {
        background: #ff4141;
    }

    .tiebaMatrix &,
    .bdtiebalive & {
        background: #7559ff;
    }

    .fortunecatMatrix &,
    .fortunecat & {
        background: #c9a687;
    }

    .youjiaMatrix &,
    .youjia & {
        background: #00cecf;
    }

    .hiphopMatrix &,
    .bdhiphop & {
        background: #5dff6c;
    }

    .baidudictMatrix &,
    .baidudict & {
        background: #00cb8a;
    }

    .baidudictMatrix &,
    .bdhealthapp & {
        background: #3ac2b2;
    }

    .tomasMatrix &,
    .tomas & {
        background-image: linear-gradient(143deg, #FF754B 0%, #FF342D 67%, #EE3B35 100%);
    }
}
