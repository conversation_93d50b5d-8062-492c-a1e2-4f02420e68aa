/*
 * @file AIHelper 工具包
 * <AUTHOR>
 * @date 2025-07-28 14:36:02
 */

import bmls from '@baidu/bdmedialive-scheme';

export const aiHelperNativeUtils = {
    sendMsg: (message = '') => {
        const encodedParams = encodeURIComponent(JSON.stringify({words: message}));
        location.href = `bmls://bdmedialive/common/sendComment?params=${encodedParams}`;
    },
    closeWebview: () => {
        bmls.common.closeWebview();
    },
    openCommentPanel: () => {
        bmls.common.openCommentPanel();
    },
    getRoomInfo: async () => {
        return await bmls.common.getRoomInfo();
    },
    // eslint-disable-next-line no-unused-vars
    openVideoPlayer: (playUrl, coverImgUrl, videoId) => {
        const encodedParams = encodeURIComponent(JSON.stringify({playUrl, coverImgUrl, videoId}));
        location.href = `bmls://bdmedialive/clue/openVideoPlayer?params=${encodedParams}`;
    },
    openAnyDoor: (url) => {
        location.href = url;
    }
};

export function format(queryRoomVideos) {
    if (!queryRoomVideos || !queryRoomVideos.videos) {
        return [];
    }

    return queryRoomVideos.videos.map(({
        title, videoSummary, videoUrl, videoCoverImg, questions, videoId, matchVideoFlag
    }, index) => {
        return {
            id: index + 1,
            title,
            content: videoSummary,
            videoUrl,
            videoCover: videoCoverImg,
            matchVideoFlag,
            videoId,
            questions: [
                ...questions.map(q => ({
                    id: videoId,
                    text: q.question
                })),
                {
                    id: 0, // 按照0
                    text: '我想问其他问题',
                    isOther: true
                }
            ]
        };
    }).sort((a, b) => {
        // videos排个序，matchVideoFlag为true 的排在第一位，其他的按照原顺序排列
        if (a.matchVideoFlag && !b.matchVideoFlag) {
            return -1;
        }
        if (!a.matchVideoFlag && b.matchVideoFlag) {
            return 1;
        }
        return 0;
    });
}