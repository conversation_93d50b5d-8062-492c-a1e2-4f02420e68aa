/* eslint-disable max-len */
/*
 * @file 直播小助手
 * <AUTHOR>
 * @date 2025-07-25 11:07:22
 */

import React, {useState, useRef, useEffect, useCallback} from 'react';
import classNames from 'classnames';
import './index.less';
import {useRequest} from 'ahooks';
import {PageStore} from '@/pages/liveshow/service/register';
import {message} from 'antd';
import {handleDoorOpenShared, showAnyDoor} from '@/pages/liveshow/components/AdLiveShow/utils/business';
import {getUrlParam} from '@/utils';
import {customQuestionClickLog, guessQuestionClickLog, outBottomBarClickLog, outBottomBarShowLog, showSummaryPanelLog, topTabClickLog, videoAreaClickLog} from '@/pages/liveshow/components/AdLiveShow/monitor/helper';
import VideoPlayer from './videoPlayer';
import {aiHelperNativeUtils, format} from './utils';
import {requestRoomEnterInfoWithAntiCheats} from './api';


export default function AIHelper({host = 'h5', h5Options = {}, nativeOptions = {}}) {
    const anydoorOptimized = getUrlParam('anydoorOptimized');
    const isH5 = host === 'h5' || anydoorOptimized;
    const {theme = 'light'} = nativeOptions;
    const extVideoId = host === 'h5' ? h5Options?.videoId : nativeOptions?.videoId;
    const {data: {
        anyMountResult = {},
        dynamicPeoplesResult = {},
        roomVideo = {}
    } = {}} = useRequest(requestRoomEnterInfoWithAntiCheats, {
        defaultParams: [PageStore.roomId, PageStore.deviceId, extVideoId]
    });
    const sections = format(roomVideo);
    const {
        anyMountCardImg = '',
        anyMountCardTitle = '',
        anyMountCardBtn = '',
        anyMountCardUrlType,
        anyMountCardUrlH5 = '',
        solutionInfo = {}
    } = anyMountResult;
    const bottomCard = {
        icon: anyMountCardImg,
        title: anyMountCardTitle,
        subtitle: dynamicPeoplesResult?.dynamicPeoplesText || '',
        buttonText: anyMountCardBtn
    };
    const from = isH5 ? 'h5' : 'native';
    const videoId
        = (roomVideo.videos || []).find(i => i.matchVideoFlag)?.videoId || '';
    useEffect(() => {
        if (videoId) {
            showSummaryPanelLog({videoId, from});
        }
    }, [from, videoId]);

    const onClick = async (e) => {
        outBottomBarClickLog({
            from,
            index: e?.target?.className || ''
        });
        if (isH5) {
            const {handleDoorOpen} = h5Options;
            if (anydoorOptimized) {
                handleDoorOpenShared('ai_helper_anydoor_optimized', {
                    anyMountCardUrlType,
                    anyMountCardUrlH5,
                    solutionInfo
                });
            }
            else {
                handleDoorOpen('ai_helper');
            }
        }
        else {
            const res = await aiHelperNativeUtils.getRoomInfo();
            const clueExt = res.data.clueExt || {};
            const rec_card_url = clueExt?.rec_card_url || '';
            if (rec_card_url) {
                aiHelperNativeUtils.openAnyDoor(rec_card_url);
            }
            else {
                message.info('任意门链接为空');
            }
        }
    };

    // 问题点击处理
    const handleQuestionClick = question => {
        const isOther = question.isOther; // 猜你想问
        if (isOther) {
            customQuestionClickLog({
                videoId: question.id,
                from
            });
            // 关闭抽屉
            aiHelperNativeUtils.closeWebview();
            // 调起评论
            aiHelperNativeUtils.openCommentPanel();
        }
        else {
            guessQuestionClickLog({
                videoId: question.id,
                questionId: question.id,
                from
            });
            // 发送消息
            aiHelperNativeUtils.sendMsg(question.text);
            // 关闭抽屉
            aiHelperNativeUtils.closeWebview();
        }
    };

    const [activeTab, setActiveTab] = useState(0);
    const containerRef = useRef(null);
    const sectionRefs = useRef([]);
    const tabsRef = useRef(null);
    const tabsScrollRef = useRef(null);
    const tabItemRefs = useRef([]);

    // 添加状态来控制是否正在进行程序化滚动
    const isScrollingProgrammatically = useRef(false);
    // 添加定时器引用来防抖滚动事件
    const scrollTimeoutRef = useRef(null);

    const showDoorFromData = showAnyDoor(anyMountResult);

    // 滚动TAB到可见区域
    const scrollTabIntoView = (index) => {
        const tabsScrollContainer = tabsScrollRef.current;
        const targetTab = tabItemRefs.current[index];

        if (!tabsScrollContainer || !targetTab) {
            return;
        }

        const containerRect = tabsScrollContainer.getBoundingClientRect();
        const tabRect = targetTab.getBoundingClientRect();

        // 计算tab相对于滚动容器的位置
        const tabLeft = tabRect.left - containerRect.left + tabsScrollContainer.scrollLeft;
        const tabRight = tabLeft + tabRect.width;

        const containerWidth = containerRect.width;
        const scrollLeft = tabsScrollContainer.scrollLeft;
        const scrollRight = scrollLeft + containerWidth;

        // 如果tab不完全可见，则滚动到合适位置
        if (tabLeft < scrollLeft) {
            // tab在左侧被遮挡，滚动到左边
            tabsScrollContainer.scrollTo({
                left: tabLeft - 16, // 16px 边距
                behavior: 'smooth'
            });
        }
        else if (tabRight > scrollRight) {
            // tab在右侧被遮挡，滚动到右边
            tabsScrollContainer.scrollTo({
                left: tabRight - containerWidth + 16, // 16px 边距
                behavior: 'smooth'
            });
        }
    };

    // 处理tab点击
    const handleTabClick = (index, {videoId}) => {
        topTabClickLog({
            videoId,
            index,
            from
        });
        setActiveTab(index);

        // 设置程序化滚动标志，防止滚动事件干扰
        isScrollingProgrammatically.current = true;

        // 滚动TAB到可见区域
        scrollTabIntoView(index);

        // 滚动到对应section
        if (sectionRefs.current[index]) {
            const offsetTop = sectionRefs.current[index].offsetTop;
            // 考虑tab栏高度
            const tabsHeight = tabsRef.current ? tabsRef.current.offsetHeight : 0;
            containerRef.current.scrollTo({
                top: offsetTop - tabsHeight - 20, // 额外20px间距
                behavior: 'smooth'
            });

            // 滚动完成后重置标志（估算滚动时间）
            setTimeout(() => {
                isScrollingProgrammatically.current = false;
            }, 800); // 给smooth滚动足够的时间完成
        }
        else {
            isScrollingProgrammatically.current = false;
        }
    };
    // 处理滚动事件，更新active tab
    const handleScroll = useCallback(() => {
        // 如果正在进行程序化滚动，忽略滚动事件
        if (isScrollingProgrammatically.current) {
            return;
        }

        if (!containerRef.current || !sectionRefs.current.length) {
            return;
        }

        // 清除之前的定时器
        if (scrollTimeoutRef.current) {
            clearTimeout(scrollTimeoutRef.current);
        }

        // 使用防抖来减少频繁的状态更新
        scrollTimeoutRef.current = setTimeout(() => {
            const scrollTop = containerRef.current.scrollTop;
            const tabsHeight = tabsRef.current ? tabsRef.current.offsetHeight : 0;
            const containerHeight = containerRef.current.clientHeight;

            // 计算当前应该激活的tab - 当内容显示一半时就激活
            let newActiveTab = 0;
            for (let i = 0; i < sectionRefs.current.length; i++) {
                const section = sectionRefs.current[i];
                if (section) {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.offsetHeight;
                    const sectionMiddle = sectionTop + sectionHeight / 2;

                    // 当section的中点进入可视区域时激活该tab
                    const viewportTop = scrollTop + tabsHeight;
                    const viewportBottom = scrollTop + containerHeight;

                    if (sectionMiddle >= viewportTop && sectionMiddle <= viewportBottom) {
                        newActiveTab = i;
                        break;
                    }
                }
            }

            if (newActiveTab !== activeTab) {
                setActiveTab(newActiveTab);
                scrollTabIntoView(newActiveTab);
            }
        }, 50); // 50ms防抖延迟
    }, [activeTab]);

    useEffect(() => {
        const container = containerRef.current;
        if (container) {
            container.addEventListener('scroll', handleScroll);
            return () => {
                container.removeEventListener('scroll', handleScroll);
                // 清理定时器
                if (scrollTimeoutRef.current) {
                    clearTimeout(scrollTimeoutRef.current);
                }
            };
        }
    }, [handleScroll]);
    useEffect(() => {
        if (showDoorFromData) {
            outBottomBarShowLog({
                from
            });
        }
    }, [from, showDoorFromData]);

    const {aiHelperDrawVisible, isPlay} = h5Options;
    const onVideoClick = () => {
        videoAreaClickLog({
            videoId: sections[activeTab].videoId,
            from
        });
    };
    return (
        <div style={{height: '100%'}}>
            <div className={classNames('ai-helper-container', `theme-${theme}`)}>
                {/* 固定的tab栏 */}
                <div className="tabs-container" ref={tabsRef}>
                    <div className="tabs-wrapper">
                        <div className="main-title">讲解回放</div>
                        <div className="tabs-scroll-container" ref={tabsScrollRef}>
                            <div className="tabs">
                                {sections.map((section, index) => (
                                    <div
                                        key={section.id}
                                        ref={el => tabItemRefs.current[index] = el}
                                        className={classNames('tab-item', {
                                            active: activeTab === index
                                        })}
                                        onClick={() => handleTabClick(index, section)}
                                    >
                                        {section.title}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>

                {/* 滚动内容区域 */}
                <div className="content-container" ref={containerRef}>
                    {sections.map((section, index) => (
                        <div
                            key={section.id}
                            className="content-section"
                            ref={el => sectionRefs.current[index] = el}
                        >
                            <div className="section-content">
                                <p className="content-text">{section.content}</p>
                                <div className="video-container">
                                    <div
                                        className="video-wrapper"
                                        onClick={onVideoClick}
                                    >
                                        <VideoPlayer
                                            index={index}
                                            section={section}
                                            isH5={isH5}
                                            aiHelperDrawVisible={aiHelperDrawVisible}
                                            isPlay={isPlay}
                                        />
                                    </div>
                                </div>
                                {/* 每个section都有自己的猜你想问 */}
                                {!isH5 && section.questions && section.questions.length > 1 && (
                                    <div className="questions-section">
                                        <div className="question-prompt">
                                            猜你想问：
                                        </div>
                                        <div className="questions-list">
                                            {section.questions.map((question) => (
                                                <div
                                                    key={question.id}
                                                    className="question-item"
                                                    onClick={() => handleQuestionClick(question, section)}
                                                >
                                                    {question.text}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>

                {/* 底部吸底卡片 */}
                {showDoorFromData && <div className="bottom-card" onClick={onClick}>
                    <div className="card-content">
                        <div className="card-left">
                            <img src={bottomCard.icon} className="doctor-icon" />
                            <div className="card-info">
                                <div className="card-title">{bottomCard.title}</div>
                                {bottomCard.subtitle && <div className="card-subtitle">{bottomCard.subtitle}</div>}
                            </div>
                        </div>
                        <button className="consult-button">
                            {bottomCard.buttonText}
                        </button>
                    </div>
                </div>}
            </div>
        </div>
    );
}

